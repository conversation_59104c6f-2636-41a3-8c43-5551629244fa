import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { useQuery } from "@tanstack/react-query";
import { Fragment } from "react";
import { useParams } from "react-router-dom";
import MessagePreview from "../features/privatemessage/components/MessagePreview";
import MessageWindow from "../features/privatemessage/components/MessageWindow";
import "@/features/privatemessage/messaging.css";
import messageImg from "@/assets/icons/navitems/messages.png";
import Spinner from "@/components/Spinners/Spinner";
import { api } from "@/helpers/api";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { cn } from "@/lib/utils";

export default function Inbox() {
    const { id: currentConvoId } = useParams();
    const { isLoading, error, data } = useQuery(api.messaging.getChatHistory.queryOptions());
    const { data: currentUser, isLoading: userLoading } = useFetchCurrentUser();
    const isMobile = useCheckMobileScreen();

    const groupedObjects = data?.reduce((acc, obj) => {
        const key = obj.senderId === currentUser?.id ? obj.receiverId : obj.senderId;
        if (!acc[key]) {
            acc[key] = [];
        }
        acc[key].push(obj);
        return acc;
    }, {});

    const groupedArray = [];
    for (const key in groupedObjects) {
        groupedArray.push({
            senderId: key === currentUser?.id ? groupedObjects[key][0].receiverId : key,
            messages: groupedObjects[key],
            unreadTotal: groupedObjects[key].filter((el) => el.read === false).length,
        });
    }

    const sortedArray = groupedArray.sort((a, b) => (a.messages[0].createdAt < b.messages[0].createdAt ? 1 : -1));

    const sentMessages = sortedArray.filter((el) => Number.parseInt(el.senderId) === currentUser?.id) || [];

    if (isLoading || userLoading) return <Spinner center />;

    if (error) return "An error has occurred: " + error.message;

    return (
        <div className="fixed flex h-[calc(100dvh-8.75rem)] w-full flex-col md:static md:h-[65dvh] -m-2">
            <div
                className={cn(
                    "relative flex h-12 gap-3 border-gray-900 border-y-2 bg-[#343549] p-1 px-4 pb-1.5 md:hidden md:rounded-t-lg"
                )}
            >
                <div className="relative my-auto h-6 w-auto">
                    <img className="h-6 w-auto" src={messageImg} alt="" />
                </div>
                <h1 className="my-auto font-medium text-lg text-stroke-s-sm text-white">Inbox</h1>

                <div className="absolute bottom-0 left-0 h-1 w-full bg-[#272839]"></div>
            </div>
            <div className="flex min-h-0 flex-1 overflow-hidden">
                {!currentConvoId && data.length === 0 ? (
                    <h2 className="mx-auto mt-12 text-center text-2xl dark:text-shadow-lg dark:text-slate-200">
                        No Messages to show!
                    </h2>
                ) : (
                    <main className="min-w-0 flex-1 border-gray-600 md:border xl:flex">
                        {isMobile && !currentConvoId ? null : (
                            <MessageWindow
                                sortedArray={sortedArray}
                                sentMessages={sentMessages[0]?.messages}
                                currentUser={currentUser}
                                convoId={currentConvoId}
                            />
                        )}

                        <aside className="xl:order-first xl:block xl:shrink-0">
                            <div className="relative flex h-full flex-col border-gray-600 bg-gray-800 md:w-64 md:border-r 2xl:w-96">
                                <div className="shrink-0">
                                    <div className="hidden h-14 flex-col justify-center bg-gray-900 px-6 text-stroke-sm md:flex">
                                        <div className="flex items-baseline space-x-3">
                                            <img className="my-auto h-6 w-auto" src={messageImg} alt="" />
                                            <h2 className="font-medium text-gray-200 text-xl">Inbox</h2>
                                        </div>
                                    </div>
                                </div>
                                <nav aria-label="Message list" className="min-h-0 flex-1 overflow-y-auto">
                                    <ul className="divide-y divide-gray-600 border-gray-600 border-y">
                                        {sortedArray.map((sender) => (
                                            <Fragment key={sender.senderId}>
                                                <MessagePreview
                                                    sender={sender}
                                                    currentConvoId={currentConvoId}
                                                    currentUserId={currentUser?.id}
                                                />
                                            </Fragment>
                                        ))}
                                    </ul>
                                </nav>
                            </div>
                        </aside>
                    </main>
                )}
            </div>
        </div>
    );
}
